import os
import json
import asyncio
import uuid
from datetime import datetime
import importlib
import google.generativeai as genai # <-- (1) إضافة المكتبة الجديدة
from dotenv import load_dotenv
from functools import wraps
from telegram import Update, ReplyKeyboardMarkup, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import (
    Application,
    CommandHandler,
    MessageHandler,
    filters,
    ContextTypes,
    ConversationHandler,
    CallbackQueryHandler,
)
from telegram.error import BadRequest
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.pdfgen import canvas
    REPORTLAB_AVAILABLE = True
except Exception:
    REPORTLAB_AVAILABLE = False

# 1. تحميل متغيرات البيئة من ملف .env
load_dotenv('.env')

# --- الإعدادات والمتغيرات الأساسية (يتم تحميلها من ملف .env) ---
TELEGRAM_TOKEN = os.getenv('TELEGRAM_TOKEN')
GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY') # <-- قراءة مفتاح جوجل
CHANNEL_ID = os.getenv('CHANNEL_ID')
GROUP_ID_STR = os.getenv('GROUP_ID')
GROUP_ID = int(GROUP_ID_STR) if GROUP_ID_STR else None
CHANNEL_LINK = os.getenv('CHANNEL_LINK')
GROUP_LINK = os.getenv('GROUP_LINK')
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')
MODEL_PRIORITY = [m.strip() for m in (os.getenv('MODEL_PRIORITY') or 'gemini,openai,anthropic').split(',') if m.strip()]

# التحقق من أن كل المتغيرات المطلوبة موجودة
if not all([TELEGRAM_TOKEN, GOOGLE_API_KEY, CHANNEL_ID, GROUP_ID, CHANNEL_LINK, GROUP_LINK]):
    raise ValueError("أحد المتغيرات المطلوبة غير موجود في ملف .env! يرجى مراجعة الملف.")

# --- (2) إعداد Google AI ---
genai.configure(api_key=GOOGLE_API_KEY)
GEMINI_MODEL_ID = 'gemini-1.5-flash'

# 2. تحميل البيانات من ملف JSON
try:
    with open('data.json', 'r', encoding='utf-8') as f:
        MAJORS_DATA = json.load(f)
except (FileNotFoundError, json.JSONDecodeError) as e:
    print(f"خطأ في تحميل ملف data.json: {e}")
    MAJORS_DATA = {}

(AI_CONVERSION,) = range(1)

# --- نظام الذكاء الاصطناعي المتطور ---
class AdvancedAI:
    def __init__(self):
        self.personality = {
            'style': 'عامية_سعودية',
            'tone': 'ودود_ومساعد',
            'expertise': 'جامعي_وأكاديمي',
            'creativity': 'عالي',
            'empathy': 'عالي'
        }
        self.context_memory = {}
        self.learning_patterns = {}

    def enhance_prompt(self, user_input: str, user_id: int, context: dict) -> str:
        """تحسين البرومبت ليكون أكثر ذكاءً وفهماً"""

        # تحليل نوع السؤال
        question_type = self.analyze_question_type(user_input)

        # بناء السياق الشخصي
        personal_context = self.get_personal_context(user_id)

        # تحسين البرومبت
        enhanced_prompt = f"""
أنت مساعد ذكي متطور جداً في بوت الديوان الجامعي. خصائصك:

🎯 الشخصية:
- تتكلم بالعامية السعودية بطريقة طبيعية وودودة
- خبير في كل المجالات الجامعية والأكاديمية
- تفهم السياق وتتذكر المحادثات السابقة
- مبدع في الحلول والاقتراحات
- متعاطف ومتفهم لاحتياجات الطلاب

🧠 قدراتك المتقدمة:
- تحليل عميق للأسئلة والمشاكل
- اقتراح حلول مبتكرة ومتعددة
- شرح المفاهيم المعقدة بطريقة بسيطة
- ربط المعلومات من مصادر متعددة
- التكيف مع أسلوب كل مستخدم

📚 تخصصك:
- جميع التخصصات الجامعية
- البحث العلمي والأكاديمي
- حل المشاكل الدراسية
- التوجيه المهني والأكاديمي
- التقنية والبرمجة

نوع السؤال المحدد: {question_type}
السياق الشخصي: {personal_context}

سؤال المستخدم: {user_input}

تعامل مع السؤال بذكاء عالي وإبداع، واستخدم العامية السعودية بطريقة طبيعية.
اجعل إجابتك مفيدة ومفصلة ومناسبة للسياق الجامعي.
"""
        return enhanced_prompt

    def analyze_question_type(self, text: str) -> str:
        """تحليل نوع السؤال لتحسين الإجابة"""
        text_lower = text.lower()

        if any(word in text_lower for word in ['كيف', 'طريقة', 'أسوي', 'اعمل']):
            return "سؤال_إجرائي"
        elif any(word in text_lower for word in ['ايش', 'وش', 'ما هو', 'عرف']):
            return "سؤال_تعريفي"
        elif any(word in text_lower for word in ['ليش', 'لماذا', 'السبب']):
            return "سؤال_تفسيري"
        elif any(word in text_lower for word in ['أفضل', 'احسن', 'انصح', 'اقترح']):
            return "طلب_اقتراح"
        elif any(word in text_lower for word in ['مشكلة', 'خطأ', 'ما يشتغل', 'عطلان']):
            return "حل_مشكلة"
        elif any(word in text_lower for word in ['اشرح', 'وضح', 'فسر']):
            return "طلب_شرح"
        else:
            return "سؤال_عام"

    def get_personal_context(self, user_id: int) -> str:
        """الحصول على السياق الشخصي للمستخدم"""
        if user_id not in self.context_memory:
            self.context_memory[user_id] = {
                'interests': [],
                'major': None,
                'level': None,
                'previous_topics': []
            }

        context = self.context_memory[user_id]
        return f"الاهتمامات: {context['interests']}, التخصص: {context['major']}, المستوى: {context['level']}"

    def update_user_context(self, user_id: int, new_info: dict):
        """تحديث السياق الشخصي للمستخدم"""
        if user_id not in self.context_memory:
            self.context_memory[user_id] = {
                'interests': [],
                'major': None,
                'level': None,
                'previous_topics': []
            }

        self.context_memory[user_id].update(new_info)

# إنشاء مثيل من الذكاء الاصطناعي المتطور
advanced_ai = AdvancedAI()

# --- دالة محسنة للتواصل مع Gemini ---
async def get_advanced_gemini_response(prompt: str, user_id: int, context: dict = None) -> str:
    try:
        # تحسين البرومبت باستخدام الذكاء المتطور
        enhanced_prompt = advanced_ai.enhance_prompt(prompt, user_id, context or {})

        # استخدام نموذج Gemini مع الإعدادات المحسنة
        model = genai.GenerativeModel(
            GEMINI_MODEL_ID,
            generation_config=genai.types.GenerationConfig(
                temperature=0.9,  # إبداع عالي
                top_p=0.95,      # تنوع في الإجابات
                top_k=40,        # خيارات متعددة
                max_output_tokens=2048,  # إجابات مفصلة
            )
        )

        def _generate():
            return model.generate_content(enhanced_prompt)

        response = await asyncio.to_thread(_generate)
        result = getattr(response, 'text', None)

        if result:
            # تحسين الإجابة وإضافة لمسات شخصية
            improved_result = await improve_response(result, prompt, user_id)
            return improved_result
        else:
            return "آسف، ما قدرت أجيب على سؤالك حاليًا. حاول تعيد صياغة السؤال بطريقة ثانية."

    except Exception as e:
        print(f"Error calling Advanced Gemini: {e}")
        return "آسف، صار خطأ تقني. بس لا تشيل هم، حاول مرة ثانية وإن شاء الله راح يشتغل معك."

async def improve_response(response: str, original_question: str, user_id: int) -> str:
    """تحسين الإجابة وإضافة لمسات شخصية"""

    # إضافة رموز تعبيرية مناسبة
    if "شكر" in original_question.lower() or "thanks" in original_question.lower():
        response += " 😊"
    elif "مشكلة" in original_question.lower():
        response += " 🤔💡"
    elif "برمجة" in original_question.lower() or "كود" in original_question.lower():
        response += " 💻"
    elif "رياضيات" in original_question.lower():
        response += " 📊"

    # إضافة عبارات تشجيعية
    encouragements = [
        "\n\nإذا تحتاج أي توضيح إضافي، لا تتردد تسأل! 🌟",
        "\n\nأتمنى إني قدرت أساعدك، وإذا عندك أسئلة ثانية أنا هنا! 💪",
        "\n\nموفق في دراستك، وأي شي تحتاجه أنا موجود! 🎓",
        "\n\nإذا الإجابة مو واضحة، قولي وراح أوضح أكثر! ✨"
    ]

    import random
    response += random.choice(encouragements)

    return response


# --- إدارة جلسة الذكاء الاصطناعي لكل مستخدم ---
def _get_user_ai_bucket(context: ContextTypes.DEFAULT_TYPE) -> dict:
    bucket = context.user_data.get('ai_session')
    if not bucket:
        bucket = {'chat': None, 'files': [], 'history': [], 'name': None}
        context.user_data['ai_session'] = bucket
    return bucket


def _ensure_user_chat_session(context: ContextTypes.DEFAULT_TYPE):
    bucket = _get_user_ai_bucket(context)
    if bucket.get('chat') is None:
        model = genai.GenerativeModel(GEMINI_MODEL_ID)
        bucket['chat'] = model.start_chat(history=[])
    return bucket['chat']


def _clear_user_chat_session(context: ContextTypes.DEFAULT_TYPE):
    context.user_data['ai_session'] = {'chat': None, 'files': [], 'history': [], 'name': None}


def _clear_user_files(context: ContextTypes.DEFAULT_TYPE):
    bucket = _get_user_ai_bucket(context)
    bucket['files'] = []


# --- تخزين الجلسات في ملف sessions.json ---
SESSIONS_FILE = 'sessions.json'

def _load_all_sessions() -> dict:
    try:
        with open(SESSIONS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception:
        return {}


def _save_all_sessions(data: dict) -> None:
    with open(SESSIONS_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def _save_user_session(user_id: int, name: str, snapshot: dict) -> None:
    data = _load_all_sessions()
    user_key = str(user_id)
    user_sessions = data.get(user_key, {})
    user_sessions[name] = snapshot
    data[user_key] = user_sessions
    _save_all_sessions(data)


def _list_user_sessions(user_id: int) -> list:
    data = _load_all_sessions()
    return list(data.get(str(user_id), {}).keys())


def _get_user_session_snapshot(user_id: int, name: str) -> dict | None:
    data = _load_all_sessions()
    return data.get(str(user_id), {}).get(name)


def _delete_user_session(user_id: int, name: str) -> bool:
    data = _load_all_sessions()
    user_key = str(user_id)
    user_sessions = data.get(user_key, {})
    if name in user_sessions:
        user_sessions.pop(name, None)
        data[user_key] = user_sessions
        _save_all_sessions(data)
        return True
    return False


def _rename_user_session(user_id: int, old_name: str, new_name: str) -> bool:
    if not new_name or new_name == old_name:
        return False
    data = _load_all_sessions()
    user_key = str(user_id)
    user_sessions = data.get(user_key, {})
    if old_name not in user_sessions or new_name in user_sessions:
        return False
    user_sessions[new_name] = user_sessions.pop(old_name)
    # حدث الاسم داخل اللقطة أيضاً
    try:
        user_sessions[new_name]['name'] = new_name
    except Exception:
        pass
    data[user_key] = user_sessions
    _save_all_sessions(data)
    return True


# --- مساعد: تنزيل أي ملف من تليجرام محلياً ---
async def download_any_telegram_file(update: Update) -> str | None:
    try:
        message = update.message
        os.makedirs('downloads', exist_ok=True)

        # صور
        if message.photo:
            photo = message.photo[-1]
            tg_file = await photo.get_file()
            file_ext = '.jpg'
        # مستندات
        elif message.document:
            tg_file = await message.document.get_file()
            name = message.document.file_name or ''
            _, ext = os.path.splitext(name)
            file_ext = ext or ''
        # فيديو
        elif message.video:
            tg_file = await message.video.get_file()
            file_ext = '.mp4'
        # صوتيات موسيقية
        elif message.audio:
            tg_file = await message.audio.get_file()
            file_ext = '.mp3'
        # رسائل صوتية
        elif message.voice:
            tg_file = await message.voice.get_file()
            file_ext = '.ogg'
        else:
            return None

        file_id = getattr(tg_file, 'file_unique_id', str(uuid.uuid4()))
        local_path = os.path.join('downloads', f"tg_{file_id}{file_ext}")
        await tg_file.download_to_drive(custom_path=local_path)
        return local_path
    except Exception as e:
        print(f"Error downloading Telegram file: {e}")
        return None


# --- مساعد: رفع ملف إلى Gemini والانتظار حتى الجاهزية ---
async def upload_file_to_gemini(file_path: str):
    try:
        def _upload():
            return genai.upload_file(path=file_path)

        uploaded = await asyncio.to_thread(_upload)

        # الانتظار حتى يصبح الملف ACTIVE
        for _ in range(30):
            try:
                def _get():
                    return genai.get_file(uploaded.name)
                info = await asyncio.to_thread(_get)
                if getattr(info, 'state', None) == 'ACTIVE':
                    return info
            except Exception:
                pass
            await asyncio.sleep(1.0)
        return uploaded  # قد يعمل حتى لو بقي PROCESSING مع نماذج معينة
    except Exception as e:
        print(f"Error uploading to Gemini: {e}")
        return None


# --- الحصول على رد من Gemini عند إرسال ملف ---
async def get_gemini_file_response(file_path: str, user_prompt: str | None) -> str:
    try:
        uploaded = await upload_file_to_gemini(file_path)
        if not uploaded:
            return "ما قدرت أرفع الملف للذكاء الاصطناعي."

        model = genai.GenerativeModel('gemini-1.5-flash')

        def _generate():
            contents = [uploaded]
            if user_prompt and user_prompt.strip():
                contents.append(user_prompt)
            else:
                contents.append("حلل هذا الملف وقدّم لي أهم النقاط باختصار.")
            return model.generate_content(contents)

        response = await asyncio.to_thread(_generate)
        return getattr(response, 'text', None) or "تمت معالجة الملف بس ما وصل رد نصي."
    except Exception as e:
        print(f"Error calling Gemini for file: {e}")
        return "آسف، فشل تحليل الملف من قبل الذكاء الاصطناعي."


# --- نظام الذكاء الاصطناعي المتطور متعدد النماذج ---
async def advanced_multimodel_generate_text(user_text: str, files: list, history: list, user_id: int) -> str:
    """نظام ذكاء اصطناعي متطور يجمع بين عدة نماذج مع تحسينات ذكية"""

    # 1) محاولة النموذج المتطور الأساسي (Gemini المحسن)
    async def try_advanced_gemini() -> str | None:
        try:
            # استخدام النظام المتطور
            context = {'history': history, 'files': files}
            return await get_advanced_gemini_response(user_text, user_id, context)
        except Exception as e:
            print(f"Advanced Gemini failed: {e}")
            return None

    # 2) نموذج Gemini العادي كبديل
    async def try_standard_gemini() -> str | None:
        try:
            model = genai.GenerativeModel(
                GEMINI_MODEL_ID,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.8,
                    top_p=0.9,
                    max_output_tokens=1500,
                )
            )

            # بناء برومبت محسن بالعامية
            enhanced_prompt = f"""
أنت مساعد ذكي في بوت الديوان الجامعي. تكلم بالعامية السعودية وكن مفيد ومبدع.

السياق السابق:
{chr(10).join([f"- {e.get('role', 'user')}: {e.get('content', '')}" for e in history[-10:]])}

سؤال المستخدم: {user_text}

اجب بطريقة ذكية ومفيدة وبالعامية السعودية.
"""

            parts = list(files) + [enhanced_prompt]
            def _gen():
                return model.generate_content(parts)
            resp = await asyncio.to_thread(_gen)
            result = getattr(resp, 'text', None)

            if result:
                # تحسين الإجابة
                return await improve_response(result, user_text, user_id)
            return None

        except Exception as e:
            print(f"Standard Gemini failed: {e}")
            return None

    # 3) OpenAI مع تحسينات
    async def try_enhanced_openai() -> str | None:
        if not OPENAI_API_KEY:
            return None
        try:
            openai_lib = importlib.import_module('openai')
            openai_lib.api_key = OPENAI_API_KEY

            # بناء رسائل محسنة
            msgs = [{
                "role": "system",
                "content": """أنت مساعد ذكي متطور في بوت الديوان الجامعي. خصائصك:
- تتكلم بالعامية السعودية بطريقة طبيعية
- خبير في جميع المجالات الجامعية
- مبدع ومفيد في إجاباتك
- تفهم السياق وتقدم حلول عملية
- ودود ومتعاطف مع الطلاب"""
            }]

            # إضافة التاريخ
            for e in history[-15:]:
                role = "user" if e.get('role') == 'user' else 'assistant'
                msgs.append({"role": role, "content": e.get('content', '')})

            msgs.append({"role": "user", "content": user_text})

            completion = openai_lib.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=msgs,
                temperature=0.8,
                max_tokens=1200
            )

            result = completion.choices[0].message["content"].strip()
            return await improve_response(result, user_text, user_id)

        except Exception as e:
            print(f"Enhanced OpenAI failed: {e}")
            return None

    # 4) Anthropic مع تحسينات
    async def try_enhanced_anthropic() -> str | None:
        if not ANTHROPIC_API_KEY:
            return None
        try:
            anthropic_lib = importlib.import_module('anthropic')
            client = anthropic_lib.Anthropic(api_key=ANTHROPIC_API_KEY)

            # بناء سياق محسن
            context_text = "السياق السابق:\n" + "\n".join([
                f"- {e.get('role', 'user')}: {e.get('content', '')}"
                for e in history[-15:]
            ])

            enhanced_prompt = f"""أنت مساعد ذكي متطور في بوت الديوان الجامعي.
تكلم بالعامية السعودية وكن مفيد ومبدع في إجاباتك.

{context_text}

سؤال المستخدم: {user_text}

اجب بذكاء وإبداع وبالعامية السعودية."""

            msg = client.messages.create(
                model="claude-3-haiku-20240307",
                max_tokens=1000,
                temperature=0.8,
                messages=[{"role": "user", "content": enhanced_prompt}]
            )

            result = msg.content[0].text if getattr(msg, 'content', None) else None
            if result:
                return await improve_response(result, user_text, user_id)
            return None

        except Exception as e:
            print(f"Enhanced Anthropic failed: {e}")
            return None

    # ترتيب المحاولات حسب الأولوية
    strategies = {
        'gemini': try_advanced_gemini,
        'openai': try_enhanced_openai,
        'anthropic': try_enhanced_anthropic,
    }

    # محاولة النماذج حسب الأولوية
    for provider in MODEL_PRIORITY:
        fn = strategies.get(provider)
        if not fn:
            continue
        text = await fn()
        if text and len(text.strip()) > 10:  # التأكد من جودة الإجابة
            return text

    # إذا فشلت كل النماذج، جرب Gemini العادي
    fallback_result = await try_standard_gemini()
    if fallback_result:
        return fallback_result

    # رسالة فشل ودودة
    return """آسف، صار خطأ تقني مؤقت 😅

بس لا تشيل هم! حاول:
• أعد كتابة السؤال بطريقة مختلفة
• أو انتظر دقيقة وحاول مرة ثانية
• أو اسأل سؤال آخر وارجع لهذا بعدين

أنا هنا عشان أساعدك! 💪"""


# --- بقية الدوال تبقى كما هي ---
# (check_membership, start, show_majors, show_services, etc...)
# ... (لا تغيير هنا) ...
def check_membership(func):
    @wraps(func)
    async def wrapped(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
        user_id = update.effective_user.id
        try:
            # التحقق من القناة
            if (await context.bot.get_chat_member(chat_id=CHANNEL_ID, user_id=user_id)).status in ['left', 'kicked']:
                keyboard = [[InlineKeyboardButton("اضغط هنا للاشتراك في القناة 📢", url=CHANNEL_LINK)]]
                await update.message.reply_text("آسف، لازم تشترك في القناة أول عشان تستخدم البوت.", reply_markup=InlineKeyboardMarkup(keyboard))
                return
            # التحقق من المجموعة
            if (await context.bot.get_chat_member(chat_id=GROUP_ID, user_id=user_id)).status in ['left', 'kicked']:
                keyboard = [[InlineKeyboardButton("اضغط هنا للانضمام للمجموعة 💬", url=GROUP_LINK)]]
                await update.message.reply_text("خطوة أخيرة! لازم تنضم للمجموعة عشان تستخدم البوت.", reply_markup=InlineKeyboardMarkup(keyboard))
                return
        except BadRequest:
            await update.message.reply_text("فيه خطأ في التحقق. تأكد إن البوت مشرف في القناة والمجموعة.")
            return
        return await func(update, context, *args, **kwargs)
    return wrapped

# --- دوال القائمة الرئيسية والأزرار ---
@check_membership
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    user_name = update.effective_user.first_name or "عزيزي المستخدم"
    kb = [
        ['التخصصات الجامعية 📚'],
        ['خدمات الطلاب 🛠️'],
        ['الدردشة مع الذكاء الاصطناعي 🤖']
    ]
    welcome_message = f'أهلين وسهلين فيك يا {user_name} في بوت الديوان الجامعي! 🎓'
    await update.message.reply_text(welcome_message, reply_markup=ReplyKeyboardMarkup(kb, resize_keyboard=True))

@check_membership
async def show_majors(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    kb = [[InlineKeyboardButton(info['name'], callback_data=f"major_{code}")] for code, info in MAJORS_DATA.items()]
    kb.append([InlineKeyboardButton("🔙 ارجع للقائمة الرئيسية", callback_data='back_to_main')])
    await update.message.reply_text('اختار التخصص اللي تبيه:', reply_markup=InlineKeyboardMarkup(kb))

@check_membership
async def show_services(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    kb = [
        [InlineKeyboardButton("حساب المعدل", callback_data='service_gpa')],
        [InlineKeyboardButton("التقويم الجامعي", callback_data='service_calendar')],
        [InlineKeyboardButton("🔙 ارجع للقائمة الرئيسية", callback_data='back_to_main')]
    ]
    await update.message.reply_text('الخدمات المتوفرة:', reply_markup=InlineKeyboardMarkup(kb))



async def button_callback_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    query = update.callback_query
    await query.answer()
    data = query.data
    
    if data.startswith('major_'):
        major_code = data.split('_')[1]
        major = MAJORS_DATA.get(major_code)
        if not major:
            await query.edit_message_text(text="آسف، هذا التخصص مو متوفر حاليًا.")
            return
        kb = [[InlineKeyboardButton(c['name'], callback_data=f"course_{major_code}_{code}")] for code, c in major.get('courses', {}).items()]
        kb.append([InlineKeyboardButton("🔙 ارجع للتخصصات", callback_data='back_to_majors')])
        await query.edit_message_text(text=f"اختار واحد من مقررات '{major['name']}':", reply_markup=InlineKeyboardMarkup(kb))
    elif data.startswith('course_'):
        _, major_code, course_code = data.split('_')
        major = MAJORS_DATA.get(major_code)
        course = (major or {}).get('courses', {}).get(course_code) if major else None
        if not (major and course):
            await query.edit_message_text(text="آسف، هذا المقرر مو متوفر حاليًا.")
            return
        text = f"{course['name']}\n\n{course['content']}"
        kb = [[InlineKeyboardButton("🔗 رابط المقرر", url=course.get('link', '#'))], [InlineKeyboardButton(f"🔙 ارجع لمقررات '{major['name']}'", callback_data=f"major_{major_code}")]]
        await query.edit_message_text(text=text, reply_markup=InlineKeyboardMarkup(kb))
    elif data == 'back_to_majors':
        # إعادة عرض قائمة التخصصات بشكل مباشر
        kb = [[InlineKeyboardButton(info['name'], callback_data=f"major_{code}")] for code, info in MAJORS_DATA.items()]
        kb.append([InlineKeyboardButton("🔙 ارجع للقائمة الرئيسية", callback_data='back_to_main')])
        await query.edit_message_text('اختار التخصص اللي تبيه:', reply_markup=InlineKeyboardMarkup(kb))
    elif data == 'back_to_main':
        # بناء لوحة البداية وإرسالها كرسالة جديدة لتفادي تمرير Message كـ Update
        await query.delete_message()
        user_name = query.from_user.first_name or "عزيزي المستخدم"
        kb = [
            ['التخصصات الجامعية 📚'],
            ['خدمات الطلاب 🛠️'],
            ['الدردشة مع الذكاء الاصطناعي 🤖']
        ]
        welcome_message = f'أهلين وسهلين فيك يا {user_name} في بوت الديوان الجامعي! 🎓'
        await context.bot.send_message(chat_id=query.message.chat_id, text=welcome_message, reply_markup=ReplyKeyboardMarkup(kb, resize_keyboard=True))
    elif data.startswith('service_'):
        kb = [[InlineKeyboardButton("🔙 ارجع للقائمة الرئيسية", callback_data='back_to_main')]]
        await query.edit_message_text(text="هذي الخدمة لسا تحت التطوير.", reply_markup=InlineKeyboardMarkup(kb))
    elif data.startswith('restore_session_'):
        # استرجاع جلسة محفوظة
        name = data.replace('restore_session_', '')
        snapshot = _get_user_session_snapshot(update.effective_user.id, name)
        if not snapshot:
            await query.edit_message_text(text='ما قدرت ألقى الجلسة اللي تبيها.')
            return
        # إعادة تهيئة الجلسة الحالية وحقن الاسم والتاريخ
        _clear_user_chat_session(context)
        bucket = _get_user_ai_bucket(context)
        bucket['name'] = snapshot.get('name') or name
        bucket['history'] = snapshot.get('history', [])
        chat = _ensure_user_chat_session(context)
        _prime_chat_with_history(chat, bucket['history'])
        await query.edit_message_text(text=f"تم استرجاع الجلسة: {bucket['name']}. تقدر تكمل النقاش الحين.")
    elif data.startswith('delete_session_'):
        name = data.replace('delete_session_', '')
        ok = _delete_user_session(update.effective_user.id, name)
        if ok:
            await query.edit_message_text(text=f"تم حذف الجلسة: {name}")
        else:
            await query.edit_message_text(text='ما قدرت أحذف الجلسة.')
    elif data.startswith('pick_rename_'):
        name = data.replace('pick_rename_', '')
        context.user_data['awaiting_rename_target'] = name
        await query.edit_message_text(text=f"اكتب الاسم الجديد للجلسة: {name}")

# --- دوال الذكاء الاصطناعي ---
@check_membership
async def start_ai_chat(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    _ensure_user_chat_session(context)
    user_name = update.effective_user.first_name or "صديقي"

    welcome_message = f"""أهلين {user_name}! 🤖✨

أنا مساعدك الذكي المتطور في الديوان الجامعي!

🧠 **قدراتي المتقدمة:**
• أفهم وأجاوب بالعامية السعودية
• أحلل الملفات (صور، مستندات، فيديوهات)
• أتذكر محادثاتنا السابقة
• أقدم حلول مبتكرة ومفصلة
• أساعد في جميع المجالات الجامعية

💡 **جرب تسأل عن:**
• شرح مفاهيم معقدة
• حل مشاكل البرمجة
• تحليل البيانات
• كتابة وتحسين النصوص
• أي شي يخص دراستك!

📎 **ارسل ملفات** وراح أحللها لك بذكاء
💬 **اكتب أي سؤال** وراح أجاوبك بإبداع

يلا ابدأ! ايش تبي تعرف؟ 🚀"""

    keyboard = [
        ['💡 أمثلة على الأسئلة', '📁 تحليل الملفات'],
        ['🎯 نصائح للاستخدام', '⚙️ إعدادات الجلسة'],
        ['ارجع للقائمة الرئيسية 🔙']
    ]

    await update.message.reply_text(
        welcome_message,
        reply_markup=ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    )
    return AI_CONVERSION

# --- معالجات الأزرار المتطورة ---
async def handle_ai_examples(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    examples_text = """💡 **أمثلة على الأسئلة المتطورة:**

🔬 **العلوم والتقنية:**
• "اشرح لي الذكاء الاصطناعي بطريقة بسيطة"
• "كيف أحل مشكلة في كود Python؟"
• "ايش الفرق بين Machine Learning و Deep Learning؟"

📚 **الدراسة والبحث:**
• "ساعدني أكتب مقدمة لبحثي عن..."
• "لخص لي هذا المقال العلمي"
• "اعطني أفكار لمشروع التخرج"

🧮 **الرياضيات والحسابات:**
• "حل لي هذه المعادلة التفاضلية"
• "اشرح لي مفهوم التكامل بالأمثلة"
• "ساعدني في الإحصاء والاحتمالات"

💼 **المهارات العملية:**
• "كيف أكتب CV مميز؟"
• "نصائح للمقابلات الشخصية"
• "ايش أفضل المهارات للمستقبل؟"

جرب أي سؤال تبيه! 🚀"""

    await update.message.reply_text(examples_text)
    return AI_CONVERSION

async def handle_file_analysis_info(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    file_info = """📁 **تحليل الملفات المتطور:**

🎯 **أنواع الملفات المدعومة:**
• 📸 الصور (JPG, PNG, GIF)
• 📄 المستندات (PDF, Word, PowerPoint)
• 🎥 الفيديوهات (MP4, AVI, MOV)
• 🎵 الملفات الصوتية (MP3, WAV)

🧠 **قدرات التحليل:**
• قراءة النصوص من الصور (OCR)
• تلخيص المستندات الطويلة
• استخراج المعلومات المهمة
• تحليل البيانات والجداول
• فهم محتوى الفيديوهات

💡 **أمثلة على الاستخدام:**
• "حلل هذه الصورة واستخرج النص"
• "لخص لي هذا الـ PDF"
• "ايش رأيك في هذا التصميم؟"
• "اشرح لي الجدول في هذا الملف"

ارسل أي ملف وراح أحلله لك بذكاء! 🔍✨"""

    await update.message.reply_text(file_info)
    return AI_CONVERSION

async def handle_usage_tips(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    tips_text = """🎯 **نصائح للاستخدام الأمثل:**

✨ **للحصول على أفضل إجابة:**
• كن واضح ومحدد في سؤالك
• اذكر السياق إذا كان مهم
• استخدم أمثلة إذا أمكن

🔄 **للمحادثات الطويلة:**
• أنا أتذكر كل محادثتنا
• تقدر تشير للأشياء اللي قلناها قبل
• استخدم "تذكر لما قلت..." للربط

📎 **مع الملفات:**
• ارسل الملف مع وصف واضح
• اسأل أسئلة محددة عن الملف
• تقدر ترسل عدة ملفات للمقارنة

🎨 **للإبداع:**
• اطلب حلول متعددة
• قل "اعطني أفكار مبتكرة"
• استخدم "فكر خارج الصندوق"

💾 **حفظ الجلسات:**
• سمي جلستك بـ /name_session
• احفظها بـ /save_session
• استرجعها بـ /load_sessions

جرب هذي النصائح وراح تشوف الفرق! 🚀"""

    await update.message.reply_text(tips_text)
    return AI_CONVERSION

async def handle_session_settings(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    settings_text = """⚙️ **إعدادات الجلسة:**

📝 **إدارة الجلسة الحالية:**
• /reset_session - تصفير الجلسة
• /clear_files - حذف الملفات المرفقة
• /name_session - تسمية الجلسة

💾 **حفظ واسترجاع:**
• /save_session - حفظ الجلسة الحالية
• /load_sessions - عرض الجلسات المحفوظة
• /delete_sessions - حذف جلسات قديمة
• /rename_sessions - إعادة تسمية الجلسات

📊 **تصدير المحادثة:**
• /export_txt - تصدير كملف نصي
• /export_pdf - تصدير كملف PDF

🔧 **معلومات الجلسة:**
• عدد الرسائل في هذه الجلسة: {len(_get_user_ai_bucket(context).get('history', []))}
• عدد الملفات المرفقة: {len(_get_user_ai_bucket(context).get('files', []))}
• اسم الجلسة: {_get_user_ai_bucket(context).get('name', 'غير محدد')}

استخدم هذي الأوامر لإدارة جلساتك بكفاءة! 💪"""

    await update.message.reply_text(settings_text)
    return AI_CONVERSION

# --- معالج المحادثة المتطور ---
async def handle_ai_conversation(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    # التقط اسم الجلسة إن كنا بانتظار الاسم
    if await maybe_capture_session_name(update, context):
        name = _get_user_ai_bucket(context).get('name') or 'جلسة'
        await update.message.reply_text(f"تم تعيين اسم الجلسة: {name}")
        return AI_CONVERSION

    # رسائل تفاعلية أثناء المعالجة
    thinking_messages = [
        'أفكر في إجابتك... 🤔',
        'أحلل سؤالك... 🧠',
        'أجهز لك إجابة مفيدة... ✨',
        'أشتغل على طلبك... 💭',
        'أبحث عن أفضل إجابة... 🔍'
    ]

    import random
    msg = await update.message.reply_text(random.choice(thinking_messages))

    _ensure_user_chat_session(context)
    bucket = _get_user_ai_bucket(context)
    files = bucket.get('files', [])
    user_id = update.effective_user.id

    try:
        # استخدام النظام المتطور
        text = await advanced_multimodel_generate_text(
            update.message.text,
            list(files),
            bucket.get('history', []),
            user_id
        )

        # تحديث السياق الشخصي للمستخدم
        advanced_ai.update_user_context(user_id, {
            'previous_topics': bucket.get('history', [])[-5:]  # آخر 5 مواضيع
        })

        await msg.edit_text(text)

        # حفظ في التاريخ المحلي
        bucket['history'].append({'role': 'user', 'content': update.message.text})
        bucket['history'].append({'role': 'model', 'content': text})

        # تحديد التخصص تلقائياً إذا أمكن
        await auto_detect_user_major(update.message.text, user_id)

    except Exception as e:
        print(f"Advanced Chat send error: {e}")
        error_messages = [
            'آسف، صار خطأ تقني بسيط. حاول مرة ثانية! 😅',
            'عذراً، فيه مشكلة مؤقتة. جرب تعيد السؤال! 🔄',
            'آسف، النظام مشغول شوي. حاول بعد ثواني! ⏳'
        ]
        await msg.edit_text(random.choice(error_messages))

    return AI_CONVERSION

async def auto_detect_user_major(user_text: str, user_id: int):
    """تحديد تخصص المستخدم تلقائياً من النص"""
    text_lower = user_text.lower()

    # كلمات مفتاحية للتخصصات
    major_keywords = {
        'علوم الحاسب': ['برمجة', 'كود', 'python', 'java', 'programming', 'algorithm', 'data structure'],
        'الهندسة': ['رياضيات', 'فيزياء', 'معادلة', 'حساب', 'تفاضل', 'تكامل', 'هندسة'],
        'الطب': ['طب', 'مرض', 'علاج', 'دواء', 'صحة', 'مستشفى'],
        'الإدارة': ['إدارة', 'أعمال', 'تسويق', 'محاسبة', 'اقتصاد', 'مالية']
    }

    for major, keywords in major_keywords.items():
        if any(keyword in text_lower for keyword in keywords):
            advanced_ai.update_user_context(user_id, {'major': major})
            break


# --- معالج الملفات المتطور ---
async def handle_ai_file_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    # رسائل تفاعلية لمعالجة الملفات
    processing_messages = [
        'أحلل الملف اللي أرسلته... 📄',
        'أشتغل على الملف... ⚡',
        'أقرأ محتوى الملف... 👀',
        'أفهم الملف وأجهز لك تحليل... 🔍',
        'أعالج الملف بذكاء... 🧠'
    ]

    import random
    msg = await update.message.reply_text(random.choice(processing_messages))

    local_path = await download_any_telegram_file(update)
    if not local_path:
        await msg.edit_text("""ما قدرت أتعرف على الملف 😅

تأكد إنك ترسل:
• صورة 📸
• مستند (PDF, Word, etc.) 📄
• فيديو 🎥
• ملف صوتي 🎵

وحاول مرة ثانية!""")
        return AI_CONVERSION

    uploaded = await upload_file_to_gemini(local_path)
    if not uploaded:
        await msg.edit_text("""آسف، ما قدرت أرفع الملف للنظام 😔

ممكن يكون:
• الملف كبير جداً
• نوع الملف مو مدعوم
• مشكلة مؤقتة في الخدمة

حاول مرة ثانية أو ارسل ملف آخر!""")
        return AI_CONVERSION

    bucket = _get_user_ai_bucket(context)
    bucket['files'].append(uploaded)
    user_id = update.effective_user.id

    # سجل الملف في التاريخ
    file_name = os.path.basename(local_path)
    bucket['history'].append({'role': 'user', 'content': f'[ملف مرفق: {file_name}]'})

    # تحسين وصف الملف
    caption = update.message.caption or 'حلل هذا الملف وأعطني أهم النقاط بالعامية السعودية'

    # إضافة سياق ذكي للملف
    enhanced_caption = f"""
أنت محلل ملفات ذكي. حلل هذا الملف بعناية:

الملف: {file_name}
طلب المستخدم: {caption}

اعطني تحليل شامل ومفيد بالعامية السعودية. اذكر:
• أهم النقاط والمعلومات
• ملاحظات مهمة
• اقتراحات أو توصيات إذا أمكن
• أي أسئلة قد تساعد المستخدم

كن مفيد ومبدع في تحليلك!
"""

    try:
        # استخدام النظام المتطور لتحليل الملف
        context_data = {'history': bucket.get('history', []), 'files': [uploaded]}
        text = await get_advanced_gemini_response(enhanced_caption, user_id, context_data)

        # تحسين النتيجة
        if text and len(text.strip()) > 20:
            # إضافة رموز تعبيرية حسب نوع الملف
            if file_name.lower().endswith(('.jpg', '.png', '.jpeg', '.gif')):
                text += " 📸"
            elif file_name.lower().endswith(('.pdf', '.doc', '.docx')):
                text += " 📄"
            elif file_name.lower().endswith(('.mp4', '.avi', '.mov')):
                text += " 🎥"
            elif file_name.lower().endswith(('.mp3', '.wav', '.ogg')):
                text += " 🎵"

            await msg.edit_text(text)
            bucket['history'].append({'role': 'model', 'content': text})
        else:
            # رسالة بديلة إذا فشل التحليل
            await msg.edit_text(f"""تم رفع الملف بنجاح! ✅

الملف: {file_name}

الملف محفوظ في الجلسة وتقدر:
• تسأل عنه أسئلة محددة
• تطلب تحليل أعمق
• ترسل ملفات إضافية للمقارنة

ايش تبي تعرف عن الملف؟ 🤔""")

    except Exception as e:
        print(f"Advanced file processing error: {e}")
        await msg.edit_text(f"""تم حفظ الملف بنجاح! ✅

الملف: {file_name}

صار خطأ بسيط في التحليل التلقائي، بس الملف محفوظ.
اسألني أي سؤال عن الملف وراح أجاوبك! 💪

مثلاً:
• "ايش أهم النقاط في الملف؟"
• "لخص لي المحتوى"
• "ايش رأيك في الملف؟" """)

    return AI_CONVERSION


# --- أوامر التحكم في الجلسة داخل الدردشة ---
async def reset_ai_session(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    _clear_user_chat_session(context)
    await update.message.reply_text('تم تصفير الجلسة. ارسل نص أو ملف عشان نكمل.', reply_markup=ReplyKeyboardMarkup([['ارجع للقائمة الرئيسية 🔙']], resize_keyboard=True))
    _ensure_user_chat_session(context)
    return AI_CONVERSION


async def clear_pinned_files(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    _clear_user_files(context)
    await update.message.reply_text('تم تفريغ الملفات المثبتة لهذي الجلسة. تقدر ترسل ملفات جديدة.')
    return AI_CONVERSION


# --- توليد ملخص للنقاش ---
def _compose_session_text_summary(bucket: dict) -> str:
    lines = []
    session_name = bucket.get('name') or 'جلسة بدون اسم'
    lines.append(f"عنوان الجلسة: {session_name}")
    lines.append(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
    lines.append("")
    for entry in bucket.get('history', []):
        role = 'المستخدم' if entry.get('role') == 'user' else 'النموذج'
        content = entry.get('content', '')
        lines.append(f"[{role}] {content}")
    return "\n".join(lines)


async def export_summary_txt(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    bucket = _get_user_ai_bucket(context)
    if not bucket.get('history'):
        await update.message.reply_text('ما فيه محتوى كافي عشان أسوي ملخص.')
        return AI_CONVERSION
    text = _compose_session_text_summary(bucket)
    os.makedirs('exports', exist_ok=True)
    name_part = (bucket.get('name') or 'session').replace(' ', '_')
    path = os.path.join('exports', f"{name_part}_summary.txt")
    with open(path, 'w', encoding='utf-8') as f:
        f.write(text)
    await context.bot.send_document(chat_id=update.effective_chat.id, document=open(path, 'rb'), filename=os.path.basename(path), caption='تم إنشاء الملخص (TXT)')
    return AI_CONVERSION


async def export_summary_pdf(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    bucket = _get_user_ai_bucket(context)
    if not bucket.get('history'):
        await update.message.reply_text('ما فيه محتوى كافي عشان أسوي ملخص.')
        return AI_CONVERSION
    if not REPORTLAB_AVAILABLE:
        await update.message.reply_text('إنشاء PDF مو متوفر حاليًا. راح أرسل TXT بدلاً منه.')
        return await export_summary_txt(update, context)
    text = _compose_session_text_summary(bucket)
    os.makedirs('exports', exist_ok=True)
    name_part = (bucket.get('name') or 'session').replace(' ', '_')
    path = os.path.join('exports', f"{name_part}_summary.pdf")
    c = canvas.Canvas(path, pagesize=A4)
    width, height = A4
    x, y = 40, height - 40
    for line in text.split('\n'):
        if y < 60:
            c.showPage()
            y = height - 40
        c.drawString(x, y, line[:110])
        y -= 16
    c.save()
    await context.bot.send_document(chat_id=update.effective_chat.id, document=open(path, 'rb'), filename=os.path.basename(path), caption='تم إنشاء الملخص (PDF)')
    return AI_CONVERSION


# --- تسمية الجلسة وحفظها واسترجاعها ---
async def ask_session_name(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    context.user_data['awaiting_session_name'] = True
    await update.message.reply_text('اكتب اسم الجلسة الحين:')
    return AI_CONVERSION


async def maybe_capture_session_name(update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
    if context.user_data.get('awaiting_session_name'):
        name = (update.message.text or '').strip()
        if name:
            bucket = _get_user_ai_bucket(context)
            bucket['name'] = name
            context.user_data['awaiting_session_name'] = False
            return True
    return False


async def save_current_session(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    bucket = _get_user_ai_bucket(context)
    if not bucket.get('name'):
        await update.message.reply_text('ما فيه اسم للجلسة. اختار "تسمية الجلسة ✍️" أول شي وبعدين حاول مرة ثانية.')
        return AI_CONVERSION
    snapshot = {
        'name': bucket.get('name'),
        'history': bucket.get('history', []),
        'saved_at': datetime.now().isoformat(timespec='seconds'),
    }
    _save_user_session(update.effective_user.id, bucket['name'], snapshot)
    await update.message.reply_text(f"تم حفظ الجلسة باسم: {bucket['name']}")
    return AI_CONVERSION


async def list_user_sessions(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    names = _list_user_sessions(update.effective_user.id)
    if not names:
        await update.message.reply_text('ما فيه جلسات محفوظة.')
        return AI_CONVERSION
    kb = [[InlineKeyboardButton(n, callback_data=f"restore_session_{n}")] for n in names]
    await update.message.reply_text('اختار جلسة عشان ترجعها:', reply_markup=InlineKeyboardMarkup(kb))
    return AI_CONVERSION


async def list_sessions_to_delete(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    names = _list_user_sessions(update.effective_user.id)
    if not names:
        await update.message.reply_text('ما فيه جلسات محفوظة عشان أحذفها.')
        return AI_CONVERSION
    kb = [[InlineKeyboardButton(f"🗑️ {n}", callback_data=f"delete_session_{n}")] for n in names]
    await update.message.reply_text('اختار جلسة عشان تحذفها (الحذف فوري):', reply_markup=InlineKeyboardMarkup(kb))
    return AI_CONVERSION


async def list_sessions_to_rename(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    names = _list_user_sessions(update.effective_user.id)
    if not names:
        await update.message.reply_text('ما فيه جلسات محفوظة عشان أغير اسمها.')
        return AI_CONVERSION
    kb = [[InlineKeyboardButton(f"✏️ {n}", callback_data=f"pick_rename_{n}")] for n in names]
    await update.message.reply_text('اختار جلسة عشان تغير اسمها:', reply_markup=InlineKeyboardMarkup(kb))
    return AI_CONVERSION


async def maybe_capture_rename_name(update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
    target = context.user_data.get('awaiting_rename_target')
    if target:
        new_name = (update.message.text or '').strip()
        if not new_name:
            await update.message.reply_text('الاسم الجديد مو صحيح. حاول مرة ثانية.')
            return True
        ok = _rename_user_session(update.effective_user.id, target, new_name)
        context.user_data['awaiting_rename_target'] = None
        if ok:
            await update.message.reply_text(f"تم تغيير اسم الجلسة من '{target}' إلى '{new_name}'.")
        else:
            await update.message.reply_text('ما قدرت أغير الاسم. ممكن الاسم موجود من قبل.')
        return True
    return False


def _prime_chat_with_history(chat, history: list):
    # نقوم بتغذية ملخص بسيط للتاريخ كتمهيد
    summary_intro = 'هذه خلاصة الجلسة السابقة كنص تمهيدي للمحادثة الحالية.'
    text = "\n".join([f"- {(e.get('role'))}: {e.get('content')}" for e in history[-30:]])
    try:
        chat.send_message([summary_intro, text])
    except Exception:
        pass


async def end_ai_chat(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    await start(update, context)
    return ConversationHandler.END

# --- الدالة الرئيسية (تبقى كما هي) ---
def main() -> None:
    app = Application.builder().token(TELEGRAM_TOKEN).build()
    ai_handler = ConversationHandler(
        entry_points=[MessageHandler(filters.Regex('^الدردشة مع الذكاء الاصطناعي 🤖$'), start_ai_chat)],
        states={
            AI_CONVERSION: [
                MessageHandler(filters.Regex('^💡 أمثلة على الأسئلة$'), handle_ai_examples),
                MessageHandler(filters.Regex('^📁 تحليل الملفات$'), handle_file_analysis_info),
                MessageHandler(filters.Regex('^🎯 نصائح للاستخدام$'), handle_usage_tips),
                MessageHandler(filters.Regex('^⚙️ إعدادات الجلسة$'), handle_session_settings),
                MessageHandler(filters.TEXT & ~filters.Regex('^ارجع للقائمة الرئيسية 🔙$') & ~filters.Regex('^💡|^📁|^🎯|^⚙️'), handle_ai_conversation),
                MessageHandler(
                    (filters.PHOTO | filters.Document.ALL | filters.VIDEO | filters.AUDIO | filters.VOICE),
                    handle_ai_file_message
                ),
            ]
        },
        fallbacks=[MessageHandler(filters.Regex('^ارجع للقائمة الرئيسية 🔙$'), end_ai_chat)],
    )
    app.add_handler(CommandHandler("start", start))
    app.add_handler(MessageHandler(filters.Regex('^التخصصات الجامعية 📚$'), show_majors))
    app.add_handler(MessageHandler(filters.Regex('^خدمات الطلاب 🛠️$'), show_services))
    app.add_handler(ai_handler)
    app.add_handler(CallbackQueryHandler(button_callback_handler))
    print("🤖 بوت الديوان الجامعي يعمل الآن...")
    print("📱 يمكنك الآن التفاعل مع البوت على تليجرام!")
    app.run_polling()

if __name__ == '__main__':
    main()