import os
import json
import asyncio
import uuid
from datetime import datetime
import importlib
import google.generativeai as genai # <-- (1) إضافة المكتبة الجديدة
from dotenv import load_dotenv
from functools import wraps
from telegram import Update, ReplyKeyboardMarkup, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import (
    Application,
    CommandHandler,
    MessageHandler,
    filters,
    ContextTypes,
    ConversationHandler,
    CallbackQueryHandler,
)
from telegram.error import BadRequest
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.pdfgen import canvas
    REPORTLAB_AVAILABLE = True
except Exception:
    REPORTLAB_AVAILABLE = False

# 1. تحميل متغيرات البيئة من ملف .env
load_dotenv('.env')

# --- الإعدادات والمتغيرات الأساسية (يتم تحميلها من ملف .env) ---
TELEGRAM_TOKEN = os.getenv('TELEGRAM_TOKEN')
GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY') # <-- قراءة مفتاح جوجل
CHANNEL_ID = os.getenv('CHANNEL_ID')
GROUP_ID_STR = os.getenv('GROUP_ID')
GROUP_ID = int(GROUP_ID_STR) if GROUP_ID_STR else None
CHANNEL_LINK = os.getenv('CHANNEL_LINK')
GROUP_LINK = os.getenv('GROUP_LINK')
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')
MODEL_PRIORITY = [m.strip() for m in (os.getenv('MODEL_PRIORITY') or 'gemini,openai,anthropic').split(',') if m.strip()]

# التحقق من أن كل المتغيرات المطلوبة موجودة
if not all([TELEGRAM_TOKEN, GOOGLE_API_KEY, CHANNEL_ID, GROUP_ID, CHANNEL_LINK, GROUP_LINK]):
    raise ValueError("أحد المتغيرات المطلوبة غير موجود في ملف .env! يرجى مراجعة الملف.")

# --- (2) إعداد Google AI ---
genai.configure(api_key=GOOGLE_API_KEY)
GEMINI_MODEL_ID = 'gemini-1.5-flash'

# 2. تحميل البيانات من ملف JSON
try:
    with open('data.json', 'r', encoding='utf-8') as f:
        MAJORS_DATA = json.load(f)
except (FileNotFoundError, json.JSONDecodeError) as e:
    print(f"خطأ في تحميل ملف data.json: {e}")
    MAJORS_DATA = {}

(AI_CONVERSION,) = range(1)

# --- (3) دالة جديدة للتواصل مع Gemini ---
async def get_gemini_response(prompt: str) -> str:
    try:
        # استخدام نسخة متزامنة من مكتبة Google AI Studio وتشغيلها في خيط منفصل
        model = genai.GenerativeModel(GEMINI_MODEL_ID)

        def _generate():
            return model.generate_content(prompt)

        response = await asyncio.to_thread(_generate)
        return getattr(response, 'text', None) or "لم يصل رد نصي من النموذج."
    except Exception as e:
        print(f"Error calling Google AI: {e}")
        return "عذراً، حدث خطأ أثناء التواصل مع الذكاء الاصطناعي. قد تكون هناك مشكلة في الخدمة حالياً."


# --- إدارة جلسة الذكاء الاصطناعي لكل مستخدم ---
def _get_user_ai_bucket(context: ContextTypes.DEFAULT_TYPE) -> dict:
    bucket = context.user_data.get('ai_session')
    if not bucket:
        bucket = {'chat': None, 'files': [], 'history': [], 'name': None}
        context.user_data['ai_session'] = bucket
    return bucket


def _ensure_user_chat_session(context: ContextTypes.DEFAULT_TYPE):
    bucket = _get_user_ai_bucket(context)
    if bucket.get('chat') is None:
        model = genai.GenerativeModel(GEMINI_MODEL_ID)
        bucket['chat'] = model.start_chat(history=[])
    return bucket['chat']


def _clear_user_chat_session(context: ContextTypes.DEFAULT_TYPE):
    context.user_data['ai_session'] = {'chat': None, 'files': [], 'history': [], 'name': None}


def _clear_user_files(context: ContextTypes.DEFAULT_TYPE):
    bucket = _get_user_ai_bucket(context)
    bucket['files'] = []


# --- تخزين الجلسات في ملف sessions.json ---
SESSIONS_FILE = 'sessions.json'

def _load_all_sessions() -> dict:
    try:
        with open(SESSIONS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception:
        return {}


def _save_all_sessions(data: dict) -> None:
    with open(SESSIONS_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def _save_user_session(user_id: int, name: str, snapshot: dict) -> None:
    data = _load_all_sessions()
    user_key = str(user_id)
    user_sessions = data.get(user_key, {})
    user_sessions[name] = snapshot
    data[user_key] = user_sessions
    _save_all_sessions(data)


def _list_user_sessions(user_id: int) -> list:
    data = _load_all_sessions()
    return list(data.get(str(user_id), {}).keys())


def _get_user_session_snapshot(user_id: int, name: str) -> dict | None:
    data = _load_all_sessions()
    return data.get(str(user_id), {}).get(name)


def _delete_user_session(user_id: int, name: str) -> bool:
    data = _load_all_sessions()
    user_key = str(user_id)
    user_sessions = data.get(user_key, {})
    if name in user_sessions:
        user_sessions.pop(name, None)
        data[user_key] = user_sessions
        _save_all_sessions(data)
        return True
    return False


def _rename_user_session(user_id: int, old_name: str, new_name: str) -> bool:
    if not new_name or new_name == old_name:
        return False
    data = _load_all_sessions()
    user_key = str(user_id)
    user_sessions = data.get(user_key, {})
    if old_name not in user_sessions or new_name in user_sessions:
        return False
    user_sessions[new_name] = user_sessions.pop(old_name)
    # حدث الاسم داخل اللقطة أيضاً
    try:
        user_sessions[new_name]['name'] = new_name
    except Exception:
        pass
    data[user_key] = user_sessions
    _save_all_sessions(data)
    return True


# --- مساعد: تنزيل أي ملف من تليجرام محلياً ---
async def download_any_telegram_file(update: Update) -> str | None:
    try:
        message = update.message
        os.makedirs('downloads', exist_ok=True)

        # صور
        if message.photo:
            photo = message.photo[-1]
            tg_file = await photo.get_file()
            file_ext = '.jpg'
        # مستندات
        elif message.document:
            tg_file = await message.document.get_file()
            name = message.document.file_name or ''
            _, ext = os.path.splitext(name)
            file_ext = ext or ''
        # فيديو
        elif message.video:
            tg_file = await message.video.get_file()
            file_ext = '.mp4'
        # صوتيات موسيقية
        elif message.audio:
            tg_file = await message.audio.get_file()
            file_ext = '.mp3'
        # رسائل صوتية
        elif message.voice:
            tg_file = await message.voice.get_file()
            file_ext = '.ogg'
        else:
            return None

        file_id = getattr(tg_file, 'file_unique_id', str(uuid.uuid4()))
        local_path = os.path.join('downloads', f"tg_{file_id}{file_ext}")
        await tg_file.download_to_drive(custom_path=local_path)
        return local_path
    except Exception as e:
        print(f"Error downloading Telegram file: {e}")
        return None


# --- مساعد: رفع ملف إلى Gemini والانتظار حتى الجاهزية ---
async def upload_file_to_gemini(file_path: str):
    try:
        def _upload():
            return genai.upload_file(path=file_path)

        uploaded = await asyncio.to_thread(_upload)

        # الانتظار حتى يصبح الملف ACTIVE
        for _ in range(30):
            try:
                def _get():
                    return genai.get_file(uploaded.name)
                info = await asyncio.to_thread(_get)
                if getattr(info, 'state', None) == 'ACTIVE':
                    return info
            except Exception:
                pass
            await asyncio.sleep(1.0)
        return uploaded  # قد يعمل حتى لو بقي PROCESSING مع نماذج معينة
    except Exception as e:
        print(f"Error uploading to Gemini: {e}")
        return None


# --- الحصول على رد من Gemini عند إرسال ملف ---
async def get_gemini_file_response(file_path: str, user_prompt: str | None) -> str:
    try:
        uploaded = await upload_file_to_gemini(file_path)
        if not uploaded:
            return "تعذر رفع الملف إلى الذكاء الاصطناعي."

        model = genai.GenerativeModel('gemini-1.5-flash')

        def _generate():
            contents = [uploaded]
            if user_prompt and user_prompt.strip():
                contents.append(user_prompt)
            else:
                contents.append("حلل هذا الملف وقدّم لي أهم النقاط باختصار.")
            return model.generate_content(contents)

        response = await asyncio.to_thread(_generate)
        return getattr(response, 'text', None) or "تمت معالجة الملف لكن لم يصل رد نصي."
    except Exception as e:
        print(f"Error calling Gemini for file: {e}")
        return "عذراً، فشل تحليل الملف من قبل الذكاء الاصطناعي."


# --- الموجه متعدد النماذج ---
async def multimodel_generate_text(user_text: str, files: list, history: list) -> str:
    # 1) حاول Gemini أولاً (أو حسب MODEL_PRIORITY)
    async def try_gemini() -> str | None:
        try:
            model = genai.GenerativeModel(GEMINI_MODEL_ID)
            parts = list(files) + [user_text]
            def _gen():
                return model.generate_content(parts)
            resp = await asyncio.to_thread(_gen)
            return getattr(resp, 'text', None)
        except Exception as e:
            print(f"Gemini failed: {e}")
            return None

    async def try_openai() -> str | None:
        if not OPENAI_API_KEY:
            return None
        try:
            openai_lib = importlib.import_module('openai')
            openai_lib.api_key = OPENAI_API_KEY
            # نبني سياق بسيط من التاريخ
            msgs = [{"role": "system", "content": "You are a helpful assistant."}]
            for e in history[-20:]:
                r = "user" if e.get('role') == 'user' else 'assistant'
                msgs.append({"role": r, "content": e.get('content', '')})
            msgs.append({"role": "user", "content": user_text})
            # استخدام Chat Completions
            completion = openai_lib.ChatCompletion.create(model="gpt-3.5-turbo", messages=msgs)
            return completion.choices[0].message["content"].strip()
        except Exception as e:
            print(f"OpenAI failed: {e}")
            return None

    async def try_anthropic() -> str | None:
        if not ANTHROPIC_API_KEY:
            return None
        try:
            anthropic_lib = importlib.import_module('anthropic')
            client = anthropic_lib.Anthropic(api_key=ANTHROPIC_API_KEY)
            # نبني سياق بسيط
            context_text = "\n".join([f"- {e.get('role')}: {e.get('content')}" for e in history[-20:]])
            msg = client.messages.create(
                model="claude-3-haiku-20240307",
                max_tokens=800,
                messages=[{"role": "user", "content": f"Context:\n{context_text}\n\nUser:\n{user_text}"}]
            )
            return (msg.content[0].text if getattr(msg, 'content', None) else None)
        except Exception as e:
            print(f"Anthropic failed: {e}")
            return None

    strategies = {
        'gemini': try_gemini,
        'openai': try_openai,
        'anthropic': try_anthropic,
    }

    for provider in MODEL_PRIORITY:
        fn = strategies.get(provider)
        if not fn:
            continue
        text = await fn()
        if text:
            return text

    return 'عذراً، جميع المزودين فشلوا حالياً. حاول لاحقاً.'


# --- بقية الدوال تبقى كما هي ---
# (check_membership, start, show_majors, show_services, etc...)
# ... (لا تغيير هنا) ...
def check_membership(func):
    @wraps(func)
    async def wrapped(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
        user_id = update.effective_user.id
        try:
            # التحقق من القناة
            if (await context.bot.get_chat_member(chat_id=CHANNEL_ID, user_id=user_id)).status in ['left', 'kicked']:
                keyboard = [[InlineKeyboardButton("اضغط هنا للاشتراك في القناة 📢", url=CHANNEL_LINK)]]
                await update.message.reply_text("عذراً، يجب عليك الاشتراك في القناة أولاً لاستخدام البوت.", reply_markup=InlineKeyboardMarkup(keyboard))
                return
            # التحقق من المجموعة
            if (await context.bot.get_chat_member(chat_id=GROUP_ID, user_id=user_id)).status in ['left', 'kicked']:
                keyboard = [[InlineKeyboardButton("اضغط هنا للانضمام للمجموعة 💬", url=GROUP_LINK)]]
                await update.message.reply_text("خطوة أخيرة! يجب عليك الانضمام للمجموعة لاستخدام البوت.", reply_markup=InlineKeyboardMarkup(keyboard))
                return
        except BadRequest:
            await update.message.reply_text("خطأ بالتحقق. تأكد أن البوت مشرف في القناة والمجموعة.")
            return
        return await func(update, context, *args, **kwargs)
    return wrapped

# --- دوال القائمة الرئيسية والأزرار ---
@check_membership
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    kb = [
        ['التخصصات الجامعية 📚'],
        ['خدمات الطلاب 🛠️'],
        ['الدردشة مع الذكاء الاصطناعي 🤖']
    ]
    await update.message.reply_text('أهلاً بك في بوت الديوان الجامعي!', reply_markup=ReplyKeyboardMarkup(kb, resize_keyboard=True))

@check_membership
async def show_majors(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    kb = [[InlineKeyboardButton(info['name'], callback_data=f"major_{code}")] for code, info in MAJORS_DATA.items()]
    kb.append([InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data='back_to_main')])
    await update.message.reply_text('الرجاء اختيار التخصص:', reply_markup=InlineKeyboardMarkup(kb))

@check_membership
async def show_services(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    kb = [
        [InlineKeyboardButton("حساب المعدل", callback_data='service_gpa')],
        [InlineKeyboardButton("التقويم الجامعي", callback_data='service_calendar')],
        [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data='back_to_main')]
    ]
    await update.message.reply_text('الخدمات المتاحة:', reply_markup=InlineKeyboardMarkup(kb))



async def button_callback_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    query = update.callback_query
    await query.answer()
    data = query.data
    
    if data.startswith('major_'):
        major_code = data.split('_')[1]
        major = MAJORS_DATA.get(major_code)
        if not major:
            await query.edit_message_text(text="عذراً، هذا التخصص غير متاح حالياً.")
            return
        kb = [[InlineKeyboardButton(c['name'], callback_data=f"course_{major_code}_{code}")] for code, c in major.get('courses', {}).items()]
        kb.append([InlineKeyboardButton("🔙 العودة للتخصصات", callback_data='back_to_majors')])
        await query.edit_message_text(text=f"اختر أحد مقررات '{major['name']}':", reply_markup=InlineKeyboardMarkup(kb))
    elif data.startswith('course_'):
        _, major_code, course_code = data.split('_')
        major = MAJORS_DATA.get(major_code)
        course = (major or {}).get('courses', {}).get(course_code) if major else None
        if not (major and course):
            await query.edit_message_text(text="عذراً، هذا المقرر غير متاح حالياً.")
            return
        text = f"{course['name']}\n\n{course['content']}"
        kb = [[InlineKeyboardButton("🔗 رابط المقرر", url=course.get('link', '#'))], [InlineKeyboardButton(f"🔙 العودة لمقررات '{major['name']}'", callback_data=f"major_{major_code}")]]
        await query.edit_message_text(text=text, reply_markup=InlineKeyboardMarkup(kb))
    elif data == 'back_to_majors':
        # إعادة عرض قائمة التخصصات بشكل مباشر
        kb = [[InlineKeyboardButton(info['name'], callback_data=f"major_{code}")] for code, info in MAJORS_DATA.items() if info.get('name') != 'علوم الحاسب والهندسة']
        await query.edit_message_text('الرجاء اختيار التخصص:', reply_markup=InlineKeyboardMarkup(kb))
    elif data == 'back_to_main':
        # بناء لوحة البداية وإرسالها كرسالة جديدة لتفادي تمرير Message كـ Update
        await query.delete_message()
        kb = [
            ['التخصصات الجامعية 📚'],
            ['خدمات الطلاب 🛠️'],
            ['الدردشة مع الذكاء الاصطناعي 🤖']
        ]
        await context.bot.send_message(chat_id=query.message.chat_id, text='أهلاً بك في بوت الديوان الجامعي!', reply_markup=ReplyKeyboardMarkup(kb, resize_keyboard=True))
    elif data.startswith('service_'):
        kb = [[InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data='back_to_main')]]
        await query.edit_message_text(text="هذه الخدمة قيد التطوير حالياً.", reply_markup=InlineKeyboardMarkup(kb))
    elif data.startswith('restore_session_'):
        # استرجاع جلسة محفوظة
        name = data.replace('restore_session_', '')
        snapshot = _get_user_session_snapshot(update.effective_user.id, name)
        if not snapshot:
            await query.edit_message_text(text='تعذر العثور على الجلسة المطلوبة.')
            return
        # إعادة تهيئة الجلسة الحالية وحقن الاسم والتاريخ
        _clear_user_chat_session(context)
        bucket = _get_user_ai_bucket(context)
        bucket['name'] = snapshot.get('name') or name
        bucket['history'] = snapshot.get('history', [])
        chat = _ensure_user_chat_session(context)
        _prime_chat_with_history(chat, bucket['history'])
        await query.edit_message_text(text=f"تم استرجاع الجلسة: {bucket['name']}. يمكنك متابعة النقاش الآن.")
    elif data.startswith('delete_session_'):
        name = data.replace('delete_session_', '')
        ok = _delete_user_session(update.effective_user.id, name)
        if ok:
            await query.edit_message_text(text=f"تم حذف الجلسة: {name}")
        else:
            await query.edit_message_text(text='تعذر حذف الجلسة.')
    elif data.startswith('pick_rename_'):
        name = data.replace('pick_rename_', '')
        context.user_data['awaiting_rename_target'] = name
        await query.edit_message_text(text=f"اكتب الاسم الجديد للجلسة: {name}")

# --- دوال الذكاء الاصطناعي ---
@check_membership
async def start_ai_chat(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    _ensure_user_chat_session(context)
    await update.message.reply_text('ابدأ الدردشة هنا. أرسل نصوص وملفات وسأتابع معك مثل ChatGPT. للعودة استخدم الأمر /start.', reply_markup=ReplyKeyboardMarkup([['العودة للقائمة الرئيسية 🔙']], resize_keyboard=True))
    return AI_CONVERSION

# --- (4) تعديل هذه الدالة لتستخدم Gemini ---
async def handle_ai_conversation(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    # التقط اسم الجلسة إن كنا بانتظار الاسم
    if await maybe_capture_session_name(update, context):
        name = _get_user_ai_bucket(context).get('name') or 'جلسة'
        await update.message.reply_text(f"تم تعيين اسم الجلسة: {name}")
        return AI_CONVERSION

    msg = await update.message.reply_text('...🤔')
    _ensure_user_chat_session(context)
    bucket = _get_user_ai_bucket(context)
    files = bucket.get('files', [])

    try:
        text = await multimodel_generate_text(update.message.text, list(files), bucket.get('history', []))
        await msg.edit_text(text)
        # حفظ في التاريخ المحلي
        bucket['history'].append({'role': 'user', 'content': update.message.text})
        bucket['history'].append({'role': 'model', 'content': text})
    except Exception as e:
        print(f"Chat send error: {e}")
        await msg.edit_text('عذراً، حدث خطأ أثناء معالجة الرسالة.')

    return AI_CONVERSION


# --- معالجة رسائل الملفات أثناء محادثة الذكاء الاصطناعي ---
async def handle_ai_file_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    msg = await update.message.reply_text('جاري معالجة الملف... ⏳')
    local_path = await download_any_telegram_file(update)
    if not local_path:
        await msg.edit_text('لم أتعرف على ملف صالح في رسالتك. أرسل صورة، مستند، فيديو أو صوت.')
        return AI_CONVERSION

    uploaded = await upload_file_to_gemini(local_path)
    if not uploaded:
        await msg.edit_text('تعذر رفع الملف إلى الذكاء الاصطناعي.')
        return AI_CONVERSION

    bucket = _get_user_ai_bucket(context)
    bucket['files'].append(uploaded)
    # سجل أن ملفاً أضيف
    try:
        bucket['history'].append({'role': 'user', 'content': f'[FILE] {os.path.basename(local_path)}'})
    except Exception:
        pass

    chat = _ensure_user_chat_session(context)
    caption = update.message.caption or 'حلل الملف المرفوع وأعطني أهم النقاط.'

    try:
        def _send():
            parts = list(bucket['files']) + [caption]
            return chat.send_message(parts)

        response = await asyncio.to_thread(_send)
        text = getattr(response, 'text', None) or 'تم تثبيت الملف. لم يصل رد نصي.'
        await msg.edit_text(text)
        bucket['history'].append({'role': 'model', 'content': text})
    except Exception as e:
        print(f"Chat file send error: {e}")
        await msg.edit_text('تم تثبيت الملف بنجاح. يمكنك مواصلة النقاش أو إرسال ملفات أخرى.')

    return AI_CONVERSION


# --- أوامر التحكم في الجلسة داخل الدردشة ---
async def reset_ai_session(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    _clear_user_chat_session(context)
    await update.message.reply_text('تم تصفير الجلسة. أرسل نصاً أو ملفاً للمتابعة.', reply_markup=ReplyKeyboardMarkup([['العودة للقائمة الرئيسية 🔙']], resize_keyboard=True))
    _ensure_user_chat_session(context)
    return AI_CONVERSION


async def clear_pinned_files(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    _clear_user_files(context)
    await update.message.reply_text('تم تفريغ الملفات المثبتة لهذه الجلسة. يمكنك إرسال ملفات جديدة.')
    return AI_CONVERSION


# --- توليد ملخص للنقاش ---
def _compose_session_text_summary(bucket: dict) -> str:
    lines = []
    session_name = bucket.get('name') or 'جلسة بدون اسم'
    lines.append(f"عنوان الجلسة: {session_name}")
    lines.append(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
    lines.append("")
    for entry in bucket.get('history', []):
        role = 'المستخدم' if entry.get('role') == 'user' else 'النموذج'
        content = entry.get('content', '')
        lines.append(f"[{role}] {content}")
    return "\n".join(lines)


async def export_summary_txt(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    bucket = _get_user_ai_bucket(context)
    if not bucket.get('history'):
        await update.message.reply_text('لا يوجد محتوى كافٍ لتوليد ملخص.')
        return AI_CONVERSION
    text = _compose_session_text_summary(bucket)
    os.makedirs('exports', exist_ok=True)
    name_part = (bucket.get('name') or 'session').replace(' ', '_')
    path = os.path.join('exports', f"{name_part}_summary.txt")
    with open(path, 'w', encoding='utf-8') as f:
        f.write(text)
    await context.bot.send_document(chat_id=update.effective_chat.id, document=open(path, 'rb'), filename=os.path.basename(path), caption='تم إنشاء الملخص (TXT)')
    return AI_CONVERSION


async def export_summary_pdf(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    bucket = _get_user_ai_bucket(context)
    if not bucket.get('history'):
        await update.message.reply_text('لا يوجد محتوى كافٍ لتوليد ملخص.')
        return AI_CONVERSION
    if not REPORTLAB_AVAILABLE:
        await update.message.reply_text('إنشاء PDF غير متاح حالياً. سيتم إرسال TXT بدلاً من ذلك.')
        return await export_summary_txt(update, context)
    text = _compose_session_text_summary(bucket)
    os.makedirs('exports', exist_ok=True)
    name_part = (bucket.get('name') or 'session').replace(' ', '_')
    path = os.path.join('exports', f"{name_part}_summary.pdf")
    c = canvas.Canvas(path, pagesize=A4)
    width, height = A4
    x, y = 40, height - 40
    for line in text.split('\n'):
        if y < 60:
            c.showPage()
            y = height - 40
        c.drawString(x, y, line[:110])
        y -= 16
    c.save()
    await context.bot.send_document(chat_id=update.effective_chat.id, document=open(path, 'rb'), filename=os.path.basename(path), caption='تم إنشاء الملخص (PDF)')
    return AI_CONVERSION


# --- تسمية الجلسة وحفظها واسترجاعها ---
async def ask_session_name(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    context.user_data['awaiting_session_name'] = True
    await update.message.reply_text('اكتب اسم الجلسة الآن:')
    return AI_CONVERSION


async def maybe_capture_session_name(update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
    if context.user_data.get('awaiting_session_name'):
        name = (update.message.text or '').strip()
        if name:
            bucket = _get_user_ai_bucket(context)
            bucket['name'] = name
            context.user_data['awaiting_session_name'] = False
            return True
    return False


async def save_current_session(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    bucket = _get_user_ai_bucket(context)
    if not bucket.get('name'):
        await update.message.reply_text('لا يوجد اسم للجلسة. اختر "تسمية الجلسة ✍️" أولاً ثم أعد المحاولة.')
        return AI_CONVERSION
    snapshot = {
        'name': bucket.get('name'),
        'history': bucket.get('history', []),
        'saved_at': datetime.now().isoformat(timespec='seconds'),
    }
    _save_user_session(update.effective_user.id, bucket['name'], snapshot)
    await update.message.reply_text(f"تم حفظ الجلسة باسم: {bucket['name']}")
    return AI_CONVERSION


async def list_user_sessions(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    names = _list_user_sessions(update.effective_user.id)
    if not names:
        await update.message.reply_text('لا توجد جلسات محفوظة.')
        return AI_CONVERSION
    kb = [[InlineKeyboardButton(n, callback_data=f"restore_session_{n}")] for n in names]
    await update.message.reply_text('اختر جلسة لاسترجاعها:', reply_markup=InlineKeyboardMarkup(kb))
    return AI_CONVERSION


async def list_sessions_to_delete(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    names = _list_user_sessions(update.effective_user.id)
    if not names:
        await update.message.reply_text('لا توجد جلسات محفوظة للحذف.')
        return AI_CONVERSION
    kb = [[InlineKeyboardButton(f"🗑️ {n}", callback_data=f"delete_session_{n}")] for n in names]
    await update.message.reply_text('اختر جلسة لحذفها (الحذف فوري):', reply_markup=InlineKeyboardMarkup(kb))
    return AI_CONVERSION


async def list_sessions_to_rename(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    names = _list_user_sessions(update.effective_user.id)
    if not names:
        await update.message.reply_text('لا توجد جلسات محفوظة لإعادة تسميتها.')
        return AI_CONVERSION
    kb = [[InlineKeyboardButton(f"✏️ {n}", callback_data=f"pick_rename_{n}")] for n in names]
    await update.message.reply_text('اختر جلسة لإعادة تسميتها:', reply_markup=InlineKeyboardMarkup(kb))
    return AI_CONVERSION


async def maybe_capture_rename_name(update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
    target = context.user_data.get('awaiting_rename_target')
    if target:
        new_name = (update.message.text or '').strip()
        if not new_name:
            await update.message.reply_text('الاسم الجديد غير صالح. حاول مرة أخرى.')
            return True
        ok = _rename_user_session(update.effective_user.id, target, new_name)
        context.user_data['awaiting_rename_target'] = None
        if ok:
            await update.message.reply_text(f"تمت إعادة تسمية الجلسة من '{target}' إلى '{new_name}'.")
        else:
            await update.message.reply_text('تعذر إعادة التسمية. قد يكون الاسم موجوداً مسبقاً.')
        return True
    return False


def _prime_chat_with_history(chat, history: list):
    # نقوم بتغذية ملخص بسيط للتاريخ كتمهيد
    summary_intro = 'هذه خلاصة الجلسة السابقة كنص تمهيدي للمحادثة الحالية.'
    text = "\n".join([f"- {(e.get('role'))}: {e.get('content')}" for e in history[-30:]])
    try:
        chat.send_message([summary_intro, text])
    except Exception:
        pass


async def end_ai_chat(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    await start(update, context)
    return ConversationHandler.END

# --- الدالة الرئيسية (تبقى كما هي) ---
def main() -> None:
    app = Application.builder().token(TELEGRAM_TOKEN).build()
    ai_handler = ConversationHandler(
        entry_points=[MessageHandler(filters.Regex('^الدردشة مع الذكاء الاصطناعي 🤖$'), start_ai_chat)],
        states={
            AI_CONVERSION: [
                MessageHandler(filters.TEXT & ~filters.Regex('^العودة للقائمة الرئيسية 🔙$'), handle_ai_conversation),
                MessageHandler(
                    (filters.PHOTO | filters.Document.ALL | filters.VIDEO | filters.AUDIO | filters.VOICE),
                    handle_ai_file_message
                ),
            ]
        },
        fallbacks=[MessageHandler(filters.Regex('^العودة للقائمة الرئيسية 🔙$'), end_ai_chat)],
    )
    app.add_handler(CommandHandler("start", start))
    app.add_handler(MessageHandler(filters.Regex('^التخصصات الجامعية 📚$'), show_majors))
    app.add_handler(MessageHandler(filters.Regex('^خدمات الطلاب 🛠️$'), show_services))
    app.add_handler(ai_handler)
    app.add_handler(CallbackQueryHandler(button_callback_handler))
    print("🤖 بوت الديوان الجامعي يعمل الآن...")
    print("📱 يمكنك الآن التفاعل مع البوت على تليجرام!")
    app.run_polling()

if __name__ == '__main__':
    main()