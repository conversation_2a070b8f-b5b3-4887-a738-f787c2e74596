{"cs": {"name": "علوم الحاسب", "courses": {"prog1": {"name": "البرمجة 1", "content": "هذا المقرر يغطي أساسيات البرمجة باستخدام لغة بايثون، بما في ذلك المتغيرات والحلقات والدوال.", "link": "https://www.youtube.com/playlist?list=PLDoPjvoNmCo_242Nf_a2p4S55_p_G_83A"}, "db": {"name": "قواعد البيانات", "content": "مقدمة إلى قواعد البيانات ونماذج SQL، وكيفية تصميم وإدارة البيانات بكفاءة.", "link": "https://example.com/link-to-db-course"}}}, "eng": {"name": "الكيمياء", "courses": {"math1": {"name": " الكيمياء العامة", "content": "❤️💖", "link": "https://t.me/U0_D0/854"}}}}