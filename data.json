{"cs": {"name": "علوم الحاسب", "courses": {"prog1": {"name": "البرمجة 1", "content": "هذا المقرر يغطي أساسيات البرمجة باستخدام لغة بايثون، بما في ذلك المتغيرات والحلقات والدوال.", "link": "https://www.youtube.com/playlist?list=PLDoPjvoNmCo_242Nf_a2p4S55_p_G_83A"}, "db": {"name": "قواعد البيانات", "content": "مقدمة إلى قواعد البيانات ونماذج SQL، وكيفية تصميم وإدارة البيانات بكفاءة.", "link": "https://example.com/link-to-db-course"}}}, "eng": {"name": "الهندسة", "courses": {"math1": {"name": "رياضيات 1", "content": "مقرر يركز على التفاضل والتكامل وتطبيقاتهما في المسائل الهندسية.", "link": "https://example.com/link-to-math1-course"}, "physics": {"name": "فيزياء عامة", "content": "أساسيات الميكانيكا الكلاسيكية، الديناميكا الحرارية، والكهرباء والمغناطيسية.", "link": "https://example.com/link-to-physics-course"}}}}